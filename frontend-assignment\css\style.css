body {
  font-family: 'Open Sans', sans-serif;
  margin: 0;
  padding: 0;
  background: #fff;
  color: #333;
}

header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 50px;
  background: #1AC073;
  color: white;
}

header ul {
  list-style: none;
  display: flex;
  gap: 20px;
}

header a {
  text-decoration: none;
  color: white;
  font-weight: 600;
}

.hero {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 50px;
  background: #2EBF91;
  color: white;
}

.hero-content h1 {
  font-size: 2.5rem;
}

.search-bar input {
  padding: 10px;
  width: 250px;
}

.search-bar button {
  padding: 10px 20px;
  background: #F3BA00;
  border: none;
  cursor: pointer;
}

.menu {
  padding: 50px;
}

.menu-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
}

.card {
  border: 1px solid #ddd;
  border-radius: 8px;
  text-align: center;
  padding: 10px;
  background: #fff;
}

.popular {
  text-align: center;
  padding: 50px;
  background: #F0FAF7;
}

.popular button {
  background: #1AC073;
  color: #fff;
  border: none;
  padding: 10px 20px;
  margin-top: 20px;
  cursor: pointer;
}

.modal {
  display: none;
  position: fixed;
  top: 0; left: 0;
  width: 100%; height: 100%;
  background: rgba(0,0,0,0.7);
  justify-content: center;
  align-items: center;
}

.modal-content {
  background: white;
  padding: 20px;
  width: 400px;
  border-radius: 8px;
}

.video-section {
  padding: 50px;
  text-align: center;
  background: #E6F9F4;
  position: relative;
}

.video-overlay {
  position: absolute;
  top: 50%; left: 50%;
  transform: translate(-50%, -50%);
  font-size: 50px;
  color: white;
  cursor: pointer;
}

.contact {
  padding: 50px;
  background: #F2F2F2;
}

footer {
  text-align: center;
  padding: 20px;
  background: #1AC073;
  color: white;
}
