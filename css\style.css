/* CSS Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Colors from Figma design */
    --primary-green: #1AC073;
    --secondary-teal: #2EBF91;
    --light-green: #F0FAF7;
    --orange: #F3BA00;
    --light-gray: #F2F2F2;
    --dark-gray: #333333;
    --text-gray: #666666;
    --white: #FFFFFF;
    
    /* Typography */
    --font-family: 'Open Sans', sans-serif;
    --font-weight-light: 300;
    --font-weight-regular: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
}

body {
    font-family: var(--font-family);
    font-weight: var(--font-weight-regular);
    line-height: 1.6;
    color: var(--dark-gray);
    background-color: var(--white);
}

/* Header Styles */
.header {
    background: var(--primary-green);
    padding: 0;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    box-shadow: 0 2px 10px rgba(26, 192, 115, 0.1);
}

.navbar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    max-width: 1440px;
    margin: 0 auto;
    padding: 16px 80px;
}

.nav-brand {
    display: flex;
    align-items: center;
    gap: 12px;
}

.logo {
    width: 32px;
    height: 32px;
}

.brand-name {
    font-size: 24px;
    font-weight: var(--font-weight-bold);
    color: var(--white);
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 40px;
}

.nav-link {
    text-decoration: none;
    color: var(--white);
    font-weight: var(--font-weight-medium);
    font-size: 16px;
    transition: opacity 0.3s ease;
    position: relative;
}

.nav-link:hover,
.nav-link.active {
    opacity: 0.8;
}

.nav-link.active::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 0;
    right: 0;
    height: 2px;
    background: var(--white);
    border-radius: 1px;
}

.nav-actions {
    display: flex;
    align-items: center;
    gap: 16px;
}

.search-btn,
.cart-btn {
    background: none;
    border: none;
    color: var(--white);
    cursor: pointer;
    padding: 8px;
    border-radius: 4px;
    transition: background-color 0.3s ease;
}

.search-btn:hover,
.cart-btn:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

/* Hero Section */
.hero-section {
    background: var(--primary-green);
    padding: 120px 0 80px;
    margin-top: 0;
}

.hero-container {
    max-width: 1440px;
    margin: 0 auto;
    padding: 0 80px;
    display: flex;
    align-items: center;
    gap: 80px;
}

.hero-content {
    flex: 1;
    max-width: 500px;
}

.hero-title {
    font-size: 48px;
    font-weight: var(--font-weight-bold);
    color: var(--white);
    line-height: 1.2;
    margin-bottom: 24px;
}

.hero-description {
    font-size: 16px;
    color: var(--white);
    opacity: 0.9;
    margin-bottom: 32px;
    line-height: 1.6;
}

.search-container {
    display: flex;
    gap: 12px;
    max-width: 400px;
}

.search-input {
    flex: 1;
    padding: 16px 20px;
    font-size: 16px;
    font-family: var(--font-family);
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.1);
    color: var(--white);
    backdrop-filter: blur(10px);
    transition: border-color 0.3s ease, background-color 0.3s ease;
}

.search-input::placeholder {
    color: rgba(255, 255, 255, 0.7);
}

.search-input:focus {
    outline: none;
    border-color: var(--white);
    background: rgba(255, 255, 255, 0.2);
}

.search-button {
    background: var(--orange);
    color: var(--white);
    border: none;
    padding: 16px 24px;
    font-size: 16px;
    font-weight: var(--font-weight-semibold);
    border-radius: 8px;
    cursor: pointer;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    white-space: nowrap;
}

.search-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(243, 186, 0, 0.3);
}

.hero-image {
    flex: 1;
    text-align: center;
}

.chef-img {
    max-width: 100%;
    height: auto;
    max-height: 500px;
}

/* Home Kitchen Section */
.home-kitchen-section {
    padding: 80px 0;
    background: var(--white);
}

.section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 40px;
}

.chef-badge {
    background: var(--primary-green);
    color: var(--white);
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: var(--font-weight-medium);
    position: relative;
}

.chef-badge::before {
    content: '';
    position: absolute;
    top: -5px;
    right: -5px;
    width: 0;
    height: 0;
    border-left: 8px solid var(--primary-green);
    border-top: 8px solid transparent;
    border-bottom: 8px solid transparent;
}

.kitchen-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
}

.kitchen-card {
    background: var(--white);
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    position: relative;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.kitchen-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
}

.discount-badge {
    position: absolute;
    top: 12px;
    left: 12px;
    background: var(--orange);
    color: var(--white);
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: var(--font-weight-bold);
    z-index: 2;
}

.kitchen-image {
    width: 100%;
    height: 160px;
    object-fit: cover;
}

.kitchen-info {
    padding: 16px;
    padding-bottom: 12px;
}

.kitchen-item-name {
    font-size: 16px;
    font-weight: var(--font-weight-medium);
    color: var(--dark-gray);
    margin-bottom: 8px;
}

.kitchen-rating {
    display: flex;
    align-items: center;
    gap: 6px;
    margin-bottom: 8px;
    font-size: 12px;
}

.kitchen-rating .stars {
    color: var(--orange);
    font-size: 14px;
}

.rating-value {
    color: var(--dark-gray);
    font-weight: var(--font-weight-medium);
}

.delivery-time {
    color: var(--text-gray);
    background: var(--light-gray);
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 11px;
}

.kitchen-price {
    font-size: 16px;
    font-weight: var(--font-weight-bold);
    color: var(--dark-gray);
}

.add-btn {
    position: absolute;
    bottom: 12px;
    right: 12px;
    width: 32px;
    height: 32px;
    background: var(--orange);
    color: var(--white);
    border: none;
    border-radius: 6px;
    font-size: 18px;
    font-weight: var(--font-weight-bold);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.3s ease, transform 0.3s ease;
}

.add-btn:hover {
    background: #e6a600;
    transform: scale(1.1);
}

/* Popular Items Section */
.popular-items-section {
    padding: 80px 0;
    background: var(--white);
    display: flex;
}

.section-container {
    max-width: 1440px;
    margin: 0 auto;
    padding: 0 80px;
}

.section-title {
    font-size: 36px;
    font-weight: var(--font-weight-bold);
    color: var(--dark-gray);
    text-align: center;
    margin-bottom: 48px;
}

.popular-carousel-container {
    position: relative;
    margin-bottom: 40px;
}

.popular-carousel-wrapper {
    overflow: hidden;
    margin: 0 60px;
}

.popular-carousel-track {
    display: flex;
    gap: 20px;
    transition: transform 0.3s ease;
}

.popular-food-card {
    flex: 0 0 300px;
    background: var(--white);
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    position: relative;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.popular-food-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
}

.popular-discount-badge {
    position: absolute;
    top: 12px;
    left: 12px;
    background: var(--orange);
    color: var(--white);
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: var(--font-weight-bold);
    z-index: 2;
}

.popular-food-image {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.popular-food-info {
    padding: 16px;
    position: relative;
}

.popular-food-name {
    font-size: 16px;
    font-weight: var(--font-weight-medium);
    color: var(--dark-gray);
    margin-bottom: 8px;
}

.popular-food-rating {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 12px;
    font-size: 12px;
}

.popular-stars {
    color: var(--orange);
    font-size: 14px;
}

.popular-rating-value {
    color: var(--dark-gray);
    font-weight: var(--font-weight-medium);
}

.popular-delivery-time {
    color: var(--text-gray);
    background: var(--light-gray);
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 11px;
}

.popular-quantity {
    color: var(--text-gray);
    background: var(--light-gray);
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 11px;
    margin-left: auto;
}

.popular-add-btn {
    width: 24px;
    height: 24px;
    background: var(--orange);
    color: var(--white);
    border: none;
    border-radius: 4px;
    font-size: 16px;
    font-weight: var(--font-weight-bold);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.3s ease;
}

.popular-add-btn:hover {
    background: #e6a600;
}

.popular-food-price {
    font-size: 18px;
    font-weight: var(--font-weight-bold);
    color: var(--dark-gray);
}

.popular-carousel-btn {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 48px;
    height: 48px;
    background: var(--white);
    border: 2px solid var(--primary-green);
    border-radius: 50%;
    color: var(--primary-green);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    z-index: 2;
}

.popular-carousel-btn:hover {
    background: var(--primary-green);
    color: var(--white);
}

.popular-carousel-btn-prev {
    left: -30px;
}

.popular-carousel-btn-next {
    right: 0;
}

.request-dish-btn {
    display: block;
    margin: 0 auto;
    padding: 12px 32px;
    background: var(--primary-green);
    color: var(--white);
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
    cursor: pointer;
    transition: all 0.3s ease;
}

.request-dish-btn:hover {
    background: #2d7a2d;
    transform: translateY(-2px);
}

/* Video Section */
.video-section {
    padding: 80px 0;
    background: var(--light-green);
}

.video-title {
    font-size: 36px;
    font-weight: var(--font-weight-bold);
    color: var(--dark-gray);
    text-align: center;
    margin-bottom: 16px;
    line-height: 1.3;
}

.video-description {
    font-size: 16px;
    color: var(--text-gray);
    text-align: center;
    margin-bottom: 48px;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.video-container {
    position: relative;
    max-width: 800px;
    margin: 0 auto;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
}

.video-player {
    width: 100%;
    height: auto;
    display: block;
}

.video-play-btn {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: none;
    border: none;
    cursor: pointer;
    transition: transform 0.3s ease;
}

.video-play-btn:hover {
    transform: translate(-50%, -50%) scale(1.1);
}

/* Contact Section */
.contact-section {
    padding: 80px 0;
    background: var(--white);
}

.contact-content {
    display: flex;
    align-items: center;
    gap: 100px;
}

.contact-form-wrapper {
    flex: 1;
    max-width: 500px;
}

.contact-title {
    font-size: 36px;
    font-weight: var(--font-weight-bold);
    color: var(--dark-gray);
    margin-bottom: 32px;
    line-height: 1.3;
}

.contact-form {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.form-input,
.form-textarea {
    padding: 16px;
    border: 2px solid var(--light-gray);
    border-radius: 8px;
    font-size: 16px;
    font-family: var(--font-family);
    transition: border-color 0.3s ease;
}

.form-input:focus,
.form-textarea:focus {
    outline: none;
    border-color: var(--primary-green);
}

.form-textarea {
    resize: vertical;
    min-height: 120px;
}

.submit-btn {
    background: var(--primary-green);
    color: var(--white);
    border: none;
    padding: 16px 32px;
    font-size: 16px;
    font-weight: var(--font-weight-semibold);
    border-radius: 8px;
    cursor: pointer;
    transition: background-color 0.3s ease;
    align-self: flex-start;
}

.submit-btn:hover {
    background: var(--secondary-teal);
}

.contact-illustration {
    flex: 1;
    text-align: center;
}

.delivery-img {
    max-width: 100%;
    height: auto;
    max-height: 400px;
}

/* Footer */
.footer {
    background: var(--primary-green);
    padding: 32px 0;
}

.footer-container {
    max-width: 1440px;
    margin: 0 auto;
    padding: 0 80px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.footer-brand {
    display: flex;
    align-items: center;
    gap: 12px;
}

.footer-logo {
    width: 24px;
    height: 24px;
}

.footer-brand-name {
    font-size: 20px;
    font-weight: var(--font-weight-bold);
    color: var(--white);
}

.footer-nav {
    display: flex;
    gap: 32px;
}

.footer-link {
    text-decoration: none;
    color: var(--white);
    font-size: 14px;
    transition: opacity 0.3s ease;
}

.footer-link:hover {
    opacity: 0.8;
}

.footer-social {
    display: flex;
    gap: 16px;
}

.social-link {
    color: var(--white);
    transition: opacity 0.3s ease;
}

.social-link:hover {
    opacity: 0.8;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 2000;
    align-items: center;
    justify-content: center;
}

.modal.active {
    display: flex;
}

.modal-content {
    background: var(--white);
    border-radius: 16px;
    max-width: 600px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    position: relative;
}

.modal-close {
    position: absolute;
    top: 16px;
    right: 16px;
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: var(--text-gray);
    z-index: 10;
}

.modal-body {
    padding: 24px;
}

.modal-image {
    width: 100%;
    height: 200px;
    object-fit: cover;
    border-radius: 8px;
    margin-bottom: 16px;
}

.modal-title {
    font-size: 24px;
    font-weight: var(--font-weight-bold);
    color: var(--dark-gray);
    margin-bottom: 8px;
}

.modal-description {
    color: var(--text-gray);
    margin-bottom: 16px;
}

.modal-rating {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 16px;
}

.modal-stars {
    color: var(--orange);
}

.modal-rating-text {
    color: var(--text-gray);
}

.modal-price {
    font-size: 24px;
    font-weight: var(--font-weight-bold);
    color: var(--primary-green);
    margin-bottom: 24px;
}

.modal-add-to-cart {
    background: var(--primary-green);
    color: var(--white);
    border: none;
    padding: 12px 24px;
    font-size: 16px;
    font-weight: var(--font-weight-semibold);
    border-radius: 8px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.modal-add-to-cart:hover {
    background: var(--secondary-teal);
}

/* Responsive Design */
@media (max-width: 1200px) {
    .navbar,
    .section-container,
    .footer-container {
        padding-left: 40px;
        padding-right: 40px;
    }

    .hero-container {
        padding-left: 40px;
        padding-right: 40px;
        gap: 40px;
    }

    .kitchen-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (max-width: 768px) {
    .navbar {
        padding: 16px 20px;
    }
    
    .nav-menu {
        display: none;
    }
    
    .hero-container {
        flex-direction: column;
        text-align: center;
        padding: 0 20px;
        gap: 40px;
    }
    
    .hero-title {
        font-size: 36px;
    }

    .search-container {
        flex-direction: column;
        max-width: 100%;
    }

    .search-input {
        margin-bottom: 0;
    }
    
    .section-container {
        padding: 0 20px;
    }
    
    .contact-content {
        flex-direction: column;
        gap: 40px;
    }
    
    .footer-container {
        flex-direction: column;
        gap: 20px;
        text-align: center;
        padding: 0 20px;
    }
    
    .carousel-btn-prev {
        left: -12px;
    }
    
    .carousel-btn-next {
        right: -12px;
    }

    .kitchen-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 16px;
    }

    .section-header {
        flex-direction: column;
        gap: 16px;
        text-align: center;
    }
}
